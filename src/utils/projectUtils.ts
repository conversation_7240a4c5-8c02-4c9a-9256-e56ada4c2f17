/**
 * Generates initials from a project name.
 * - If two or more words, takes first letter of first two words.
 * - If one word, takes first two letters of that word.
 * - Returns uppercase initials.
 */
export const getProjectInitials = (name: string): string => {
  if (!name || name.trim() === "") {
    return "";
  }

  const words = name.trim().split(/\s+/);

  if (words.length >= 2) {
    // Take first letter of first two words
    return (words[0][0] + words[1][0]).toUpperCase();
  } else {
    // Take first two letters of the single word
    return words[0].substring(0, 2).toUpperCase();
  }
};
