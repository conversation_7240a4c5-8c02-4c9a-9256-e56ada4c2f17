<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + HeroUI</title>
    <meta key="title" content="Vite + HeroUI" property="og:title" />
    <meta
      content="Make beautiful websites regardless of your design experience."
      property="og:description"
    />
    <meta
      content="Make beautiful websites regardless of your design experience."
      name="description"
    />
    <meta
      key="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <link href="/favicon.ico" rel="icon" />
    <script>
      (function () {
        try {
          const theme = localStorage.getItem("theme");
          if (
            theme === "dark" ||
            (!theme &&
              window.matchMedia("(prefers-color-scheme: dark)").matches)
          ) {
            document.documentElement.classList.add("dark");
          } else {
            document.documentElement.classList.remove("dark");
          }
        } catch (e) {}
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
