import { Navigate, Outlet, useLocation, useParams } from "react-router-dom";
import { useEffect, useState } from "react";

import { getProject, Project } from "@/services/main/projectMainService.ts";

const ProtectedProjectRoute = () => {
  const location = useLocation();
  const { projectId } = useParams();
  const [project, setProject] = useState<Project | null>(null);
  const [projectLoadStatus, setProjectLoadStatus] = useState<
    "none" | "loading" | "error" | "success"
  >("none");

  useEffect(() => {
    if (!projectId) {
      return;
    }
    setProjectLoadStatus("loading");
    const fetchProject = async () => {
      try {
        const response = await getProject(projectId);

        if (response.data.data) {
          setProject(response.data.data);
          setProjectLoadStatus("success");
        }
      } catch {
        setProjectLoadStatus("error");
      }
    };

    fetchProject();
  }, [location.pathname]);

  if (!projectId || projectLoadStatus === "error") {
    return <Navigate replace={true} to="/" />;
  }

  return (
    <Outlet
      context={{
        project,
        projectLoadStatus,
      }}
    />
  );
};

export default ProtectedProjectRoute;
