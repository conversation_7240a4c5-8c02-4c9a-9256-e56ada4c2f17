import type { ChatListItem, NormalizedMessage } from "../types";

// Mock chat list data
export const mockChats: ChatListItem[] = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    phoneNumber: "+62812-3456-7890",
    whatsappId: "sari_wijaya_wa",
    avatar: undefined,
    lastMessage: {
      content: "Hai! Apa kabar?",
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      sender: "contact",
      type: "text",
      whatsappMessageId: "wamid.1234567890",
    },
    unreadCount: 2,
    isBlocked: false,
    isMuted: false,
    labels: ["Important", "Work"],
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    phoneNumber: "+62813-9876-5432",
    whatsappId: "miko_chandra_wa",
    avatar: undefined,
    lastMessage: {
      content: "Sempurna! <PERSON>ri tahu saya saat Anda tiba.",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      sender: "user",
      type: "text",
      whatsappMessageId: "wamid.0987654321",
    },
    unreadCount: 0,
    isBlocked: false,
    isMuted: false,
    labels: ["Friends"],
  },
  {
    id: "3",
    name: "Ema Dewi",
    phoneNumber: "+62814-5678-9012",
    whatsappId: "ema_dewi_wa",
    avatar: undefined,
    lastMessage: {
      content: "📷 Foto",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
      sender: "contact",
      type: "image",
      whatsappMessageId: "wamid.1357924680",
    },
    unreadCount: 1,
    isBlocked: false,
    isMuted: false,
    labels: ["Family"],
  },
  {
    id: "4",
    name: "Alex Rahman",
    phoneNumber: "+62815-2109-8765",
    whatsappId: "alex_rahman_wa",
    avatar: undefined,
    lastMessage: {
      content: "Terima kasih atas dokumennya!",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      sender: "contact",
      type: "text",
      whatsappMessageId: "wamid.2468135790",
    },
    unreadCount: 0,
    isBlocked: false,
    isMuted: false,
    labels: ["Work", "Clients"],
  },
  {
    id: "5",
    name: "Lisa Wati",
    phoneNumber: "+62816-5432-1098",
    whatsappId: "lisa_wati_wa",
    avatar: undefined,
    lastMessage: {
      content: "🎵 Audio",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
      sender: "contact",
      type: "audio",
      whatsappMessageId: "wamid.1122334455",
    },
    unreadCount: 3,
    isBlocked: false,
    isMuted: false,
    labels: ["Personal"],
  },
  {
    id: "6",
    name: "David Kusuma",
    phoneNumber: "+62817-8901-2345",
    whatsappId: "david_kusuma_wa",
    avatar: undefined,
    lastMessage: {
      content: "Bisakah kita jadwalkan rapat minggu depan?",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
      sender: "contact",
      type: "text",
      whatsappMessageId: "wamid.5566778899",
    },
    unreadCount: 0,
    isBlocked: false,
    isMuted: false,
    labels: ["Work"],
  },
  {
    id: "7",
    name: "Maya Putri",
    phoneNumber: "+62818-0123-4567",
    whatsappId: "maya_putri_wa",
    avatar: undefined,
    lastMessage: {
      content: "📄 Dokumen",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 4).toISOString(), // 4 days ago
      sender: "user",
      type: "document",
      whatsappMessageId: "wamid.9988776655",
    },
    unreadCount: 1,
    isBlocked: false,
    isMuted: false,
    labels: ["Clients"],
  },
  {
    id: "8",
    name: "Johan Santoso",
    phoneNumber: "+62819-3456-7890",
    whatsappId: "johan_santoso_wa",
    avatar: undefined,
    lastMessage: {
      content: "Presentasi bagus hari ini!",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
      sender: "contact",
      type: "text",
      whatsappMessageId: "wamid.3344556677",
    },
    unreadCount: 0,
    isBlocked: false,
    isMuted: false,
    labels: ["Work", "Team"],
  },
];

// Mock messages data
export const mockMessages: Record<string, NormalizedMessage[]> = {
  "1": [
    {
      id: "msg_1_1",
      chatId: "1",
      whatsappMessageId: "wamid.msg_1_1",
      content: "Hai Sari! Saya baik-baik saja, terima kasih sudah bertanya.",
      type: "text",
      sender: {
        id: "current-user",
        name: "You",
        phoneNumber: "+62811-1234-5678",
        whatsappId: "user_wa_id",
        type: "user",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
      status: "read",
    },
    {
      id: "msg_1_2",
      chatId: "1",
      whatsappMessageId: "wamid.msg_1_2",
      content: "Bagaimana proyekmu?",
      type: "text",
      sender: {
        id: "current-user",
        name: "You",
        phoneNumber: "+62811-1234-5678",
        whatsappId: "user_wa_id",
        type: "user",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 59).toISOString(), // 59 minutes ago
      status: "read",
    },
    {
      id: "msg_1_3",
      chatId: "1",
      whatsappMessageId: "wamid.msg_1_3",
      content:
        "Berjalan dengan baik! Kami baru saja menyelesaikan fase pertama.",
      type: "text",
      sender: {
        id: "1",
        name: "Sari Wijaya",
        phoneNumber: "+62812-3456-7890",
        whatsappId: "sari_wijaya_wa",
        type: "contact",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
      status: "read",
    },
    {
      id: "msg_1_4",
      chatId: "1",
      whatsappMessageId: "wamid.msg_1_4",
      content: "Itu luar biasa! Selamat 🎉",
      type: "text",
      sender: {
        id: "current-user",
        name: "You",
        phoneNumber: "+62811-1234-5678",
        whatsappId: "user_wa_id",
        type: "user",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 40).toISOString(), // 40 minutes ago
      status: "read",
    },
    {
      id: "msg_1_5",
      chatId: "1",
      whatsappMessageId: "wamid.msg_1_5",
      content:
        "Terima kasih! Apakah Anda ingin minum kopi minggu ini untuk merayakan?",
      type: "text",
      sender: {
        id: "1",
        name: "Sari Wijaya",
        phoneNumber: "+62812-3456-7890",
        whatsappId: "sari_wijaya_wa",
        type: "contact",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 35).toISOString(), // 35 minutes ago
      status: "read",
    },
    {
      id: "msg_1_6",
      chatId: "1",
      whatsappMessageId: "wamid.msg_1_6",
      content: "Hai! Apa kabar?",
      type: "text",
      sender: {
        id: "1",
        name: "Sari Wijaya",
        phoneNumber: "+62812-3456-7890",
        whatsappId: "sari_wijaya_wa",
        type: "contact",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      status: "delivered",
    },
  ],
  "2": [
    {
      id: "msg_2_1",
      chatId: "2",
      whatsappMessageId: "wamid.msg_2_1",
      content: "Hai Miko, apakah kita masih janjian makan siang hari ini?",
      type: "text",
      sender: {
        id: "current-user",
        name: "You",
        phoneNumber: "+62811-1234-5678",
        whatsappId: "user_wa_id",
        type: "user",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
      status: "read",
    },
    {
      id: "msg_2_2",
      chatId: "2",
      whatsappMessageId: "wamid.msg_2_2",
      content: "Tentu saja! Sampai jumpa pukul 12:30 di tempat biasa kita.",
      type: "text",
      sender: {
        id: "2",
        name: "Miko Chandra",
        phoneNumber: "+62813-9876-5432",
        whatsappId: "miko_chandra_wa",
        type: "contact",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2.5).toISOString(), // 2.5 hours ago
      status: "read",
    },
    {
      id: "msg_2_3",
      chatId: "2",
      whatsappMessageId: "wamid.msg_2_3",
      content: "Sempurna! Beri tahu saya saat Anda tiba.",
      type: "text",
      sender: {
        id: "current-user",
        name: "You",
        phoneNumber: "+62811-1234-5678",
        whatsappId: "user_wa_id",
        type: "user",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      status: "read",
    },
  ],
  "3": [
    {
      id: "msg_3_1",
      chatId: "3",
      whatsappMessageId: "wamid.msg_3_1",
      content: "Hai Ema! Bagaimana akhir pekammu?",
      type: "text",
      sender: {
        id: "current-user",
        name: "You",
        phoneNumber: "+62811-1234-5678",
        whatsappId: "user_wa_id",
        type: "user",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
      status: "read",
    },
    {
      id: "msg_3_2",
      chatId: "3",
      whatsappMessageId: "wamid.msg_3_2",
      content: "Luar biasa! Pergi hiking bersama keluarga.",
      type: "text",
      sender: {
        id: "3",
        name: "Ema Dewi",
        phoneNumber: "+62814-5678-9012",
        whatsappId: "ema_dewi_wa",
        type: "contact",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5.5).toISOString(), // 5.5 hours ago
      status: "read",
    },
    {
      id: "msg_3_3",
      chatId: "3",
      whatsappMessageId: "wamid.msg_3_3",
      content: "Lihat pemandangan indah ini dari puncak!",
      type: "image",
      sender: {
        id: "3",
        name: "Ema Dewi",
        phoneNumber: "+62814-5678-9012",
        whatsappId: "ema_dewi_wa",
        type: "contact",
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
      status: "delivered",
    },
  ],
};

// Function to initialize store with mock data
export const initializeMockData = () => {
  return {
    chats: mockChats,
    messages: mockMessages,
  };
};
