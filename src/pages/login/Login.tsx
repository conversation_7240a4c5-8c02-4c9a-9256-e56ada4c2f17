import React, { useEffect, useState } from "react";
import { Button, Input, Link } from "@heroui/react";
import { Eye, EyeOff, Mail, Lock } from "lucide-react";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import AuthFooter from "@/components/auth/AuthFooter.tsx";

export default function Login() {
  const auth = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const inputClassNames = {
    input:
      "text-gray-700 placeholder:text-gray-400 dark:text-gray-100 dark:placeholder:text-gray-500",
    inputWrapper:
      "bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-blue-300 focus-within:border-blue-500 transition-all duration-200 dark:bg-gray-800/80 dark:border-gray-600 dark:hover:border-blue-400 dark:focus-within:border-blue-400",
    label: "text-gray-600 dark:text-gray-300",
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await auth.login(email, password);
  };

  useEffect(() => {
    if (auth.state === "loggedIn") {
      setTimeout(() => {
        window.location.href = "/";
      }, 1000);
    }
  }, [auth.state]);

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Left side - Background Image */}
      <div
        className="hidden lg:flex lg:w-2/3 relative bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80')`,
        }}
      >
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/10 to-transparent dark:from-blue-900/40 dark:via-purple-900/20 dark:to-transparent" />

        {/* Content Overlay */}
        <div className="absolute inset-0 flex items-center justify-center p-12">
          <div className="text-center text-white max-w-md">
            <h2 className="text-4xl font-bold mb-4 drop-shadow-lg">
              Welcome Back
            </h2>
            <p className="text-lg opacity-90 drop-shadow">
              Sign in to continue your journey with Ideal Lumatera
            </p>
          </div>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div className="w-full lg:w-1/3 flex flex-col justify-center p-8 lg:p-12 bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
        {/* Main Content Container */}
        <div className="flex flex-col space-y-8 max-w-md mx-auto w-full">
          {/* Header with Logo */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">IL</span>
              </div>
            </div>
            <div className="space-y-2">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Ideal Lumatera
              </h2>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                Welcome Back
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Sign in to your account to continue
              </p>
            </div>
          </div>

          {/* Login Form */}
          <div className="space-y-6">
            {auth.errorMessages && (
              <div
                className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-600 dark:border-red-500/40 dark:bg-red-950 dark:text-red-200"
                role="alert"
              >
                {auth.errorMessages}
              </div>
            )}
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-5">
                <div>
                  <Input
                    required
                    classNames={inputClassNames}
                    label="Email"
                    labelPlacement="inside"
                    placeholder="Enter your email address"
                    size="lg"
                    startContent={
                      <Mail className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    }
                    type="email"
                    value={email}
                    variant="bordered"
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>

                <div>
                  <Input
                    required
                    classNames={inputClassNames}
                    endContent={
                      <button
                        className="focus:outline-none"
                        type="button"
                        onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                      >
                        {isPasswordVisible ? (
                          <EyeOff className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                        )}
                      </button>
                    }
                    label="Password"
                    labelPlacement="inside"
                    placeholder="Enter your password"
                    size="lg"
                    startContent={
                      <Lock className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                    }
                    type={isPasswordVisible ? "text" : "password"}
                    value={password}
                    variant="bordered"
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
              </div>

              <Button
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-200 dark:from-blue-500 dark:to-purple-500 dark:hover:from-blue-600 dark:hover:to-purple-600"
                isLoading={auth.state === "loggingIn"}
                size="lg"
                type="submit"
              >
                Sign in
              </Button>
            </form>

            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Don&apos;t have an account?{" "}
              </span>
              <Link
                className="text-blue-600 hover:text-blue-700 font-medium dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                href="/signup"
                size="sm"
              >
                Sign up now
              </Link>
            </div>
          </div>

          {/* Footer */}
          <AuthFooter className="mt-8" />
        </div>
      </div>
    </div>
  );
}
