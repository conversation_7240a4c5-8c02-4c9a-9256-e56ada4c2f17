// Mock data untuk halaman home

// Interface untuk recent chats
export interface RecentChat {
  id: string;
  contactName: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  avatar?: string;
  isOnline: boolean;
}

// Interface untuk statistik pengguna
export interface UserStats {
  totalChats: number;
  unreadMessages: number;
  totalContacts: number;
  todayMessages: number;
}

// Interface untuk quick actions
export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: string; // action type untuk routing
  color?: string;
}

// Mock data untuk recent chats
export const mockRecentChats: RecentChat[] = [
  {
    id: "1",
    contactName: "<PERSON>",
    lastMessage: "Hey, how are you doing today?",
    timestamp: "10:30 AM",
    unreadCount: 2,
    isOnline: true,
  },
  {
    id: "2",
    contactName: "<PERSON>",
    lastMessage: "Thanks for the help yesterday!",
    timestamp: "9:15 AM",
    unreadCount: 0,
    isOnline: true,
  },
  {
    id: "3",
    contactName: "<PERSON>",
    lastMessage: "Can we schedule a meeting?",
    timestamp: "Yesterday",
    unreadCount: 1,
    isOnline: false,
  },
  {
    id: "4",
    contactName: "Emily Davis",
    lastMessage: "Great work on the project! 👏",
    timestamp: "Yesterday",
    unreadCount: 0,
    isOnline: true,
  },
  {
    id: "5",
    contactName: "Team Chat",
    lastMessage: "Meeting at 3 PM today",
    timestamp: "2 days ago",
    unreadCount: 3,
    isOnline: false,
  },
];

// Mock data untuk statistik pengguna
export const mockUserStats: UserStats = {
  totalChats: 15,
  unreadMessages: 6,
  totalContacts: 42,
  todayMessages: 8,
};

// Mock data untuk quick actions
export const mockQuickActions: QuickAction[] = [
  {
    id: "1",
    label: "New Chat",
    icon: "MessageCircle",
    action: "/chats",
    color: "blue",
  },
  {
    id: "2",
    label: "Contacts",
    icon: "Users",
    action: "/contacts",
    color: "green",
  },
  {
    id: "3",
    label: "Search",
    icon: "Search",
    action: "/chats",
    color: "purple",
  },
  {
    id: "4",
    label: "Settings",
    icon: "Settings",
    action: "/settings",
    color: "gray",
  },
];
