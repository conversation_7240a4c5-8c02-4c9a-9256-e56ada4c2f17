# Struktur Firestore

Ada Collection `Projects`
Di dalam collection `Projects` ada list documents dengan struktur seperti berikut.

```json
{
  "uid": "**********",
  "projectName": "Project Name",
  "description": "Project Description",
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z",
  "whatsappCredentials": {
    "phoneNumber": "+*************",
    "provider": "meta",
    "bearerToken": "bearer_token",
    "whatsappBusinessAccountId": "business_account_id",
    "phoneNumberId": "phone_number_id",
    "webhookVerificationToken": "verification_token"
  },
  "ownerUserUid": "user_id"
}
```

Di dalam doc project ada subcollection `chats`. Representasi dari percakapan yang ada di project ini.
Di dalam subcollection `chats` ada list documents dengan struktur seperti berikut.

```json
{
  "name": "Chat Name",
  "description": "Chat Description",
  "phoneNumber": "+*************",
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z",
  "lastMessage": {}
}
```

`lastMessage` berisi message terakhir yang dikirim ke chat ini. Isi nya sama dengan document di `messages`.

Di dalam document `chats` ada subcollection `messages`. Representasi isi percakapan yang ada di chat ini.

```json
{
  "messageId": "message_id",
  "content": "message_content",
  "type": "message_type", // text, image, audio, video, document, sticker, location, contact, button_reply, list_reply, reaction, system
  "text": {
    "body": "text_content"
  },
  "image": {
    "id": "image_id",
    "mime_type": "image/jpeg",
    "sha256": "image_sha256",
    "caption": "image_caption"
  },
  "context": {
    "from": "sender_phone_number",
    "id": "message_id"
  },
  "createdAt": "2024-01-15T10:00:00Z",
  "direction": "IN",
  "lastStatus": "delivered",
  "statuses": {
    "read": "2024-01-15T10:00:00Z",
    "delivered": "2024-01-15T10:00:00Z",
    "sent": "2024-01-15T10:00:00Z",
    "failed": "2024-01-15T10:00:00Z"
  },
  "error": {
    "messages": "error_messages",
    "metadata": "error_metadata"
  }
}
```
