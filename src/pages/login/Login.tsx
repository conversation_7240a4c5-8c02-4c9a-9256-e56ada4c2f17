import React, { useEffect, useState } from "react";
import { Button, Input, Link } from "@heroui/react";

import { useAuth } from "@/components/providers/AuthProvider.tsx";

export default function Login() {
  const auth = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const inputClassNames = {
    input:
      "text-gray-700 placeholder:text-gray-400 dark:text-gray-100 dark:placeholder:text-gray-500",
    inputWrapper:
      "bg-gray-50 border border-gray-200 hover:border-gray-300 transition-colors dark:bg-gray-800 dark:border-gray-700 dark:hover:border-gray-600",
    label: "text-gray-600 dark:text-gray-300",
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await auth.login(email, password);
  };

  useEffect(() => {
    if (auth.state === "loggedIn") {
      setTimeout(() => {
        window.location.href = "/";
      }, 1000);
    }
  }, [auth.state]);

  return (
    <div className="min-h-screen flex bg-gray-50 dark:bg-gray-950">
      {/* Left side - Background Image */}
      <div
        className="hidden lg:flex lg:w-2/3 relative bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=serene%20coastal%20landscape%20with%20lighthouse%20and%20sailboat%20at%20sunset%2C%20mountains%20in%20background%2C%20calm%20waters%2C%20peaceful%20atmosphere%2C%20photorealistic%20style&image_size=landscape_16_9')`,
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20 dark:bg-black/40" />

      </div>

      {/* Right side - Login Form */}
      <div className="w-full lg:w-1/3 flex flex-col justify-center p-12 bg-white dark:bg-gray-900">
        {/* Main Content Container */}
        <div className="flex flex-col space-y-8 max-w-sm mx-auto w-full">
          {/* Header with Logo */}
          <div className="flex items-center justify-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">TI</span>
            </div>
            <span className="ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100">
              Trimitra Ideal
            </span>
          </div>

          {/* Welcome Text */}
          <div className="text-center space-y-2">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
              Nice to see you again
            </h1>
          </div>

          {/* Login Form */}
          <div className="space-y-6">
            {auth.errorMessages && (
              <div
                className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-600 dark:border-red-500/40 dark:bg-red-950 dark:text-red-200"
                role="alert"
              >
                {auth.errorMessages}
              </div>
            )}
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <Input
                    required
                    classNames={inputClassNames}
                    label="Login"
                    labelPlacement="inside"
                    placeholder="Email or phone number"
                    size="lg"
                    type="email"
                    value={email}
                    variant="flat"
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>

                <div>
                  <Input
                    required
                    classNames={inputClassNames}
                    label="Password"
                    labelPlacement="inside"
                    placeholder="Enter password"
                    size="lg"
                    type="password"
                    value={password}
                    variant="flat"
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
              </div>

              <Button
                className="w-full bg-blue-600 hover:bg-blue-700 font-semibold dark:bg-blue-500 dark:hover:bg-blue-400"
                color="primary"
                isLoading={auth.state === "loggingIn"}
                size="lg"
                type="submit"
              >
                Sign in
              </Button>
            </form>

            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Don't have an account?{" "}
              </span>
              <Link
                className="text-blue-600 hover:text-blue-700 font-medium dark:text-blue-400 dark:hover:text-blue-300"
                href="/signup"
                size="sm"
              >
                Sign up now
              </Link>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between text-sm text-gray-500 mt-8 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">F</span>
              </div>
              <span className="text-blue-600 dark:text-blue-400">
                @trimitra
              </span>
            </div>
            <span>© Ideal Lumatera 2024</span>
          </div>
        </div>
      </div>
    </div>
  );
}
