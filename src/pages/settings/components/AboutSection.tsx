import { Info, ExternalLink, FileText, Shield } from "lucide-react";
import { Link } from "@heroui/react";

interface SectionProps {
  className?: string;
}

export default function AboutSection({ className = "" }: SectionProps) {
  const appVersion = "1.0.0";
  const buildNumber = "2024.01.15";

  const handleTermsClick = () => {
    // TODO: Navigate to terms of service page
    console.log("Terms of service clicked");
  };

  const handlePrivacyClick = () => {
    // TODO: Navigate to privacy policy page
    console.log("Privacy policy clicked");
  };

  const handleSupportClick = () => {
    // TODO: Navigate to support page
    console.log("Support clicked");
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm ${className}`}
    >
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        About
      </h2>

      <div className="space-y-6">
        {/* App Information */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Info className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Ideal Lumatera
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                A modern chat application
              </p>
            </div>
          </div>

          <div className="ml-8 space-y-1">
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Version: <span className="font-mono">{appVersion}</span>
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Build: <span className="font-mono">{buildNumber}</span>
            </p>
          </div>
        </div>

        {/* Legal Links */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Legal
          </h3>

          <div className="space-y-2 ml-2">
            <Link
              className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              href="#"
              onClick={handleTermsClick}
            >
              <FileText className="w-4 h-4" />
              <span>Terms of Service</span>
              <ExternalLink className="w-3 h-3" />
            </Link>

            <Link
              className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              href="#"
              onClick={handlePrivacyClick}
            >
              <Shield className="w-4 h-4" />
              <span>Privacy Policy</span>
              <ExternalLink className="w-3 h-3" />
            </Link>
          </div>
        </div>

        {/* Support */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Support
          </h3>

          <div className="ml-2">
            <Link
              className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              href="#"
              onClick={handleSupportClick}
            >
              <Info className="w-4 h-4" />
              <span>Help &amp; Support</span>
              <ExternalLink className="w-3 h-3" />
            </Link>
          </div>
        </div>

        {/* Copyright */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-500 text-center">
            © 2024 Ideal Lumatera. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
