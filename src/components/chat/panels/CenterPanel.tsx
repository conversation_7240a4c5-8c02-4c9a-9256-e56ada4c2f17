import type { CenterPanelProps } from "@/types";

import React from "react";

import ChatHeader from "./ChatHeader";
import MessageArea from "./MessageArea";
import MessageInput from "./MessageInput";

import { showMobileChatListView } from "@/store/uiSlice.ts";
import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";

const CenterPanel: React.FC<CenterPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { activeChatId, chats } = useAppSelector((state) => state.chat);
  const { screenSize } = useAppSelector((state) => state.ui);

  const activeChat = activeChatId
    ? chats.find((chat) => chat.id === activeChatId)
    : null;

  // Show welcome screen when no chat is selected
  if (!activeChat) {
    return (
      <div
        className={`flex items-center justify-center h-full bg-white dark:bg-gray-800 ${className}`}
      >
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </div>

          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-3">
            Welcome to Ideal Trimitra Chat
          </h2>

          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Select a chat from the sidebar to start messaging with your
            contacts.
          </p>

          <div className="space-y-3 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  clipRule="evenodd"
                  d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                  fillRule="evenodd"
                />
              </svg>
              <span>End-to-end encrypted</span>
            </div>

            <div className="flex items-center justify-center space-x-2">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  clipRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  fillRule="evenodd"
                />
              </svg>
              <span>WhatsApp Business API</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Chat Header */}
      <ChatHeader
        chat={activeChat}
        onBackClick={
          screenSize === "mobile"
            ? () => dispatch(showMobileChatListView())
            : undefined
        }
      />

      {/* Message Area */}
      <div className="flex-1 overflow-hidden bg-white dark:bg-gray-800">
        <MessageArea chatId={activeChat.id} />
      </div>

      {/* Message Input */}
      <MessageInput chatId={activeChat.id} />
    </div>
  );
};

export default CenterPanel;
