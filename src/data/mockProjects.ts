// Mock project interface for demo purposes
export interface MockProject {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive" | "archived";
  startDate: string;
  endDate: string;
  members: string[];
  tags: string[];
  avatar?: string;
  whatsappCredentials?: {
    phoneNumber: string;
    provider: string;
    bearerToken: string;
    whatsappBusinessAccountId: string;
    phoneNumberId: string;
    webhookVerificationToken: string;
  };
}

// Mock project data
export const mockProjects: MockProject[] = [
  {
    id: "1",
    name: "E-Commerce Platform Development",
    description:
      "Building a comprehensive e-commerce platform with payment integration and inventory management.",
    status: "active",
    startDate: new Date(Date.now() - 100 * 60 * 24 * 30).toISOString(), // 30 days ago
    endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days from now
    members: ["1", "2", "3"],
    tags: ["Development", "Web", "E-commerce"],
    avatar: undefined,
  },
  {
    id: "2",
    name: "Mobile Banking Application System",
    description:
      "Developing a secure mobile banking application with biometric authentication and real-time transactions.",
    status: "active",
    startDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45).toISOString(), // 45 days ago
    endDate: new Date(Date.now() + 1000 * 60 * 24 * 30).toISOString(), // 30 days from now
    members: ["4", "5", "6"],
    tags: ["Mobile", "Finance", "Security"],
    avatar: undefined,
  },
  {
    id: "3",
    name: "Healthcare Management System Integration",
    description:
      "Integrating multiple healthcare systems for patient records, appointments, and billing.",
    status: "active",
    startDate: new Date(Date.now() - 1000 * 60 * 24 * 90).toISOString(), // 90 days ago
    endDate: new Date(Date.now() + 1000 * 60 * 24 * 15).toISOString(), // 15 days from now
    members: ["7", "8", "9"],
    tags: ["Healthcare", "Integration", "API"],
    avatar: undefined,
  },
  {
    id: "4",
    name: "Learning Management Platform with AI Features",
    description:
      "Creating an AI-powered learning platform with personalized course recommendations and progress tracking.",
    status: "active",
    startDate: new Date(Date.now() - 1000 * 60 * 24 * 20).toISOString(), // 20 days ago
    endDate: new Date(Date.now() + 1000 * 60 * 24 * 120).toISOString(), // 120 days from now
    members: ["10", "11", "12"],
    tags: ["Education", "AI", "Web"],
    avatar: undefined,
  },
  {
    id: "5",
    name: "Restaurant POS System",
    description:
      "Developing a point-of-sale system for restaurants with inventory tracking and customer loyalty features.",
    status: "inactive",
    startDate: new Date(Date.now() - 1000 * 60 * 24 * 120).toISOString(), // 120 days ago
    endDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(), // 10 days ago
    members: ["13", "14"],
    tags: ["POS", "Restaurant", "Mobile"],
    avatar: undefined,
  },
  {
    id: "6",
    name: "Real Estate CRM Platform",
    description:
      "Building a customer relationship management platform for real estate agents with property listing features.",
    status: "archived",
    startDate: new Date(Date.now() - 1000 * 60 * 24 * 365).toISOString(), // 365 days ago
    endDate: new Date(Date.now() - 1000 * 60 * 24 * 30).toISOString(), // 30 days ago
    members: ["15", "16", "17"],
    tags: ["CRM", "Real Estate", "Web"],
    avatar: undefined,
  },
];
