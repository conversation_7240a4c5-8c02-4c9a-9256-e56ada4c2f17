import type { ChatListItem as ChatListItemType } from "../../../types";

import { memo } from "react";
import dayjs from "dayjs";

import { useAppDispatch } from "../../../store/hooks";
import { openRightPanel } from "../../../store/uiSlice";

interface ChatListItemProps {
  chat: ChatListItemType;
  isActive: boolean;
  onClick: () => void;
}
const formatTime = (timestamp: string): string => {
  const now = dayjs();
  const messageTime = dayjs(timestamp);
  const diffInHours = now.diff(messageTime, "hour");

  if (diffInHours < 24) {
    // Show time for today
    return messageTime.format("HH:mm");
  } else if (diffInHours < 48) {
    // Show "Yesterday" for yesterday
    return "Yesterday";
  } else if (diffInHours < 168) {
    // Show day name for this week
    return messageTime.format("ddd");
  } else {
    // Show date for older messages
    return messageTime.format("MMM D");
  }
};

const truncateMessage = (message: string, maxLength: number = 50): string => {
  if (message.length <= maxLength) return message;

  return message.substring(0, maxLength) + "...";
};

const ChatListItem: React.FC<ChatListItemProps> = memo(
  ({ chat, isActive, onClick }) => {
    const dispatch = useAppDispatch();

    const handleChatClick = () => {
      onClick();
      // Open right panel when chat is selected (desktop only)
      dispatch(openRightPanel());
    };

    const getMessageTypeIcon = (type: string) => {
      switch (type) {
        case "image":
          return "📷 ";
        case "audio":
          return "🎵 ";
        case "video":
          return "🎥 ";
        case "document":
          return "📄 ";
        case "location":
          return "📍 ";
        case "contact":
          return "👤 ";
        default:
          return "";
      }
    };

    return (
      <div
        className={`
        flex items-center p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 
        transition-colors duration-150 relative group
        ${isActive ? "bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500" : ""}
      `}
        onClick={handleChatClick}
      >
        {/* Avatar */}
        <div className="relative flex-shrink-0 mr-3">
          <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600">
            {chat.avatar ? (
              <img
                alt={chat.name}
                className="w-full h-full object-cover"
                src={chat.avatar}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500 text-white font-medium text-base">
                {chat.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </div>

        {/* Chat Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-0.5">
            <div className="flex items-center space-x-1.5 min-w-0">
              <h3
                className={`
              font-medium truncate text-sm
              ${isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-900 dark:text-white"}
              ${chat.unreadCount > 0 ? "font-semibold" : ""}
            `}
              >
                {chat.name}
              </h3>
            </div>

            <div className="flex items-center space-x-1.5 flex-shrink-0">
              {/* Time */}
              <span
                className={`
              text-xs
              ${
                chat.unreadCount > 0
                  ? "text-blue-600 dark:text-blue-400 font-medium"
                  : "text-gray-500 dark:text-gray-400"
              }
            `}
              >
                {formatTime(chat.lastMessage.timestamp)}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            {/* Last Message */}
            <div className="flex-1 min-w-0">
              <p
                className={`
              text-sm truncate
              ${
                chat.unreadCount > 0
                  ? "text-gray-900 dark:text-white font-medium"
                  : "text-gray-600 dark:text-gray-400"
              }
            `}
              >
                {getMessageTypeIcon(chat.lastMessage.type)}
                {chat.lastMessage.sender === "user" && (
                  <span className="text-blue-600 dark:text-blue-400">
                    You:{" "}
                  </span>
                )}
                {truncateMessage(chat.lastMessage.content)}
              </p>

              {/* Labels */}
              {chat.labels.length > 0 && (
                <div className="flex items-center space-x-1 mt-1">
                  {chat.labels.slice(0, 2).map((label) => (
                    <span
                      key={label}
                      className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-0.5 rounded-full"
                    >
                      {label}
                    </span>
                  ))}
                  {chat.labels.length > 2 && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      +{chat.labels.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Unread Badge & Actions */}
            <div className="flex items-center space-x-1.5 flex-shrink-0 ml-2">
              {chat.unreadCount > 0 && (
                <span className="bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center font-medium">
                  {chat.unreadCount > 99 ? "99+" : chat.unreadCount}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  },
);

ChatListItem.displayName = "ChatListItem";

export default ChatListItem;
