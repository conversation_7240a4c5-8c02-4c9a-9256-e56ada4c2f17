import type { LeftPanelProps } from "@/types";

import React, { useMemo, useRef } from "react";
import { Input } from "@heroui/input";
import { Button } from "@heroui/button";
import { useDisclosure } from "@heroui/modal";

import ChatListItem from "./ChatListItem";
import FilterDrawer from "./FilterDrawer";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { setActiveChat, setSearchQuery } from "@/store/chatSlice.ts";
import { setMainMenuExpanded, showMobileChatView } from "@/store/uiSlice.ts";

// Hamburger Menu Icon
const MenuIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M4 6h16M4 12h16M4 18h16"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

// Search Icon
const SearchIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

// Filter Icon
const FilterIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const LeftPanel: React.FC<LeftPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { chats, searchQuery, activeChatId } = useAppSelector(
    (state) => state.chat,
  );
  const { screenSize } = useAppSelector((state) => state.ui);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  // Filter and search logic
  const filteredChats = useMemo(() => {
    let filtered = [...chats];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();

      filtered = filtered.filter((item) => {
        const nameMatch = item.name.toLowerCase().includes(query);
        const cleanedQuery = query.replace(/\D/g, "");
        const phoneMatch = cleanedQuery
          ? item.phoneNumber.replace(/\D/g, "").includes(cleanedQuery)
          : false;

        return nameMatch || phoneMatch;
      });
    }

    // Sort chats
    // Sort chats by last message timestamp
    filtered.sort(
      (a, b) =>
        new Date(b.lastMessage.timestamp).getTime() -
        new Date(a.lastMessage.timestamp).getTime(),
    );

    return filtered;
  }, [chats, searchQuery]);

  const handleChatSelect = (chatId: string) => {
    dispatch(setActiveChat(chatId));

    // On mobile, switch to chat view
    if (screenSize === "mobile") {
      dispatch(showMobileChatView());
    }
  };

  const handleOpenMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };

  const handleSearchChange = (value: string) => {
    dispatch(setSearchQuery(value));
  };

  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={containerRef}
      className={`relative flex flex-col h-full ${className}`}
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {/* Mobile Hamburger Menu Button */}
            {screenSize === "mobile" && (
              <Button
                isIconOnly
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                size="sm"
                variant="light"
                onPress={handleOpenMainMenu}
              >
                <MenuIcon />
              </Button>
            )}
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Chats
            </h1>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              isIconOnly
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              size="sm"
              variant="light"
              onPress={onOpen}
            >
              <FilterIcon />
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Input
            className="w-full"
            classNames={{
              input: "text-sm",
              inputWrapper:
                "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600",
            }}
            placeholder="Search chats..."
            startContent={<SearchIcon />}
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
          />
        </div>

        {/* Active Filters */}
        <div className="relative mt-4">
          <div className="flex flex-nowrap gap-2 overflow-x-auto scroll-smooth transparent-scrollbar">
            {[
              { type: "Label", value: "Work" },
              { type: "Department", value: "Customer Support Department" },
              { type: "Label", value: "HR" },
              { type: "Department", value: "Project Management Team" },
              { type: "Label", value: "IT" },
              { type: "Department", value: "Quality Assurance Division" },
              { type: "Department", value: "Business Development Unit" },
            ].map((filter, index) => {
              const fullText = `${filter.type}: ${filter.value}`;
              const truncatedText =
                fullText.length > 25
                  ? fullText.substring(0, 22) + "..."
                  : fullText;

              return (
                <div
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 flex-shrink-0"
                >
                  <span title={fullText}>{truncatedText}</span>
                  <button
                    className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                    onClick={() =>
                      console.log(
                        `Removing filter: ${filter.type}: ${filter.value}`,
                      )
                    }
                  >
                    ×
                  </button>
                </div>
              );
            })}
          </div>
          {/* Fade gradients for scroll indicators */}
          <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-gray-50 dark:from-gray-700 to-transparent pointer-events-none" />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No chats found</p>
            {searchQuery && (
              <Button
                className="mt-2"
                size="sm"
                variant="light"
                onPress={() => dispatch(setSearchQuery(""))}
              >
                Clear search
              </Button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-100 dark:divide-gray-700">
            {filteredChats.map((chat) => (
              <ChatListItem
                key={chat.id}
                chat={chat}
                isActive={chat.id === activeChatId}
                onClick={() => handleChatSelect(chat.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>{filteredChats.length} chats</span>
        </div>
      </div>

      <FilterDrawer isOpen={isOpen} onOpenChange={onOpenChange} />
    </div>
  );
};

export default LeftPanel;
