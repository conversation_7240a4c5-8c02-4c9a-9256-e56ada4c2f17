import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>rkles } from "lucide-react";

interface WelcomeSectionProps {
  className?: string;
  userName?: string;
}

const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  className = "",
  userName = "User",
}) => {
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();

    if (hour < 12) {
      return {
        greeting: "Good Morning",
        icon: Sun,
        message: "Ready to start your day with some great conversations?",
        bgGradient: "from-yellow-400 to-orange-500",
        textColor: "text-yellow-800",
      };
    } else if (hour < 17) {
      return {
        greeting: "Good Afternoon",
        icon: Coffee,
        message: "Hope your day is going well! Time for some productive chats.",
        bgGradient: "from-blue-400 to-cyan-500",
        textColor: "text-blue-800",
      };
    } else {
      return {
        greeting: "Good Evening",
        icon: Moon,
        message: "Winding down? Perfect time to catch up with your contacts.",
        bgGradient: "from-purple-400 to-indigo-600",
        textColor: "text-purple-800",
      };
    }
  };

  const timeData = getTimeBasedGreeting();
  const IconComponent = timeData.icon;

  const getCurrentDate = () => {
    const today = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };

    return today.toLocaleDateString("en-US", options);
  };

  return (
    <div className={`relative overflow-hidden rounded-lg ${className}`}>
      {/* Background with gradient */}
      <div
        className={`bg-gradient-to-br ${timeData.bgGradient} p-6 relative dark:bg-gradient-to-br dark:from-gray-800 dark:to-gray-900`}
      >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 opacity-20">
          <Sparkles className="w-full h-full text-white" />
        </div>
        <div className="absolute bottom-0 left-0 w-24 h-24 opacity-10">
          <Star className="w-full h-full text-white" />
        </div>

        {/* Main content */}
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
              <IconComponent className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white dark:text-gray-100">
                {timeData.greeting}, {userName}!
              </h1>
              <p className="text-white/80 text-sm font-medium dark:text-gray-300">
                {getCurrentDate()}
              </p>
            </div>
          </div>

          <p className="text-white/90 text-sm leading-relaxed mb-4 dark:text-gray-200">
            {timeData.message}
          </p>

          {/* Quick stats or motivational element */}
          <div className="flex items-center gap-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2 dark:bg-gray-700/30">
              <p className="text-white text-xs font-medium dark:text-gray-200">
                Today's Goal
              </p>
              <p className="text-white text-sm font-bold dark:text-gray-100">
                Stay Connected 💬
              </p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2 dark:bg-gray-700/30">
              <p className="text-white text-xs font-medium dark:text-gray-200">
                Mood
              </p>
              <p className="text-white text-sm font-bold dark:text-gray-100">
                Productive ✨
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom section with tips */}
      <div className="bg-white border-x border-b border-gray-200 p-4 dark:bg-gray-800 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              All systems operational
            </span>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Last updated: just now
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeSection;
