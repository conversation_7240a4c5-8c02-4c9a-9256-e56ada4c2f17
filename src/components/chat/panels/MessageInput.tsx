import { useState, useRef } from "react";
import { But<PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";

import { useAppDispatch } from "../../../store/hooks";
import { sendMessage } from "../../../store/chatSlice";

interface MessageInputProps {
  chatId: string;
}

// Icons
const SendIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const AttachIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const EmojiIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const MicIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const MessageInput: React.FC<MessageInputProps> = ({ chatId }) => {
  const dispatch = useAppDispatch();
  const [message, setMessage] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSendMessage = () => {
    if (message.trim()) {
      dispatch(
        sendMessage({
          chatId,
          content: message.trim(),
          type: "text",
        }),
      );
      setMessage("");

      // Focus back to input
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleAttachment = () => {
    // TODO: Implement file attachment
    console.log("Attachment clicked");
  };

  const handleEmojiClick = () => {
    // TODO: Implement emoji picker
    console.log("Emoji clicked");
  };

  const handleVoiceRecord = () => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      // TODO: Process recorded audio
      console.log("Stop recording");
    } else {
      // Start recording
      setIsRecording(true);
      // TODO: Start voice recording
      console.log("Start recording");
    }
  };

  return (
    <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="flex items-end space-x-2">
        {/* Attachment Button */}
        <Button
          isIconOnly
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex-shrink-0 w-8 h-8"
          size="sm"
          variant="light"
          onPress={handleAttachment}
        >
          <div className="w-4 h-4">
            <AttachIcon />
          </div>
        </Button>

        {/* Message Input Container */}
        <div className="flex-1 flex items-end space-x-2">
          {/* Text Input */}
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              classNames={{
                input: "text-sm py-2",
                inputWrapper:
                  "min-h-[36px] bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 rounded-2xl",
              }}
              endContent={
                <Button
                  isIconOnly
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 w-6 h-6"
                  size="sm"
                  variant="light"
                  onPress={handleEmojiClick}
                >
                  <div className="w-4 h-4">
                    <EmojiIcon />
                  </div>
                </Button>
              }
              placeholder="Type a message..."
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
            />
          </div>

          {/* Send/Voice Button */}
          {message.trim() ? (
            <Button
              isIconOnly
              className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-9 h-9 flex-shrink-0"
              color="primary"
              size="sm"
              onPress={handleSendMessage}
            >
              <div className="w-4 h-4">
                <SendIcon />
              </div>
            </Button>
          ) : (
            <Button
              isIconOnly
              className={`
                w-9 h-9 rounded-full flex-shrink-0 transition-colors
                ${
                  isRecording
                    ? "bg-red-500 text-white hover:bg-red-600"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                }
              `}
              size="sm"
              variant="light"
              onPress={handleVoiceRecord}
            >
              <div className="w-4 h-4">
                <MicIcon />
              </div>
            </Button>
          )}
        </div>
      </div>

      {/* Recording indicator */}
      {isRecording && (
        <div className="flex items-center justify-center mt-2 text-red-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium">Recording...</span>
          </div>
        </div>
      )}

      {/* Typing indicator placeholder */}
      {/* You can add a typing indicator here when someone else is typing */}
    </div>
  );
};

export default MessageInput;
