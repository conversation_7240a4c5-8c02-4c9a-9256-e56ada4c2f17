import type { NormalizedMessage } from "../../../types";

import { memo } from "react";

interface MessageBubbleProps {
  message: NormalizedMessage;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
}

// Message status icons
const MessageStatusIcon = ({
  status,
}: {
  status: NormalizedMessage["status"];
}) => {
  switch (status) {
    case "sending":
      return (
        <svg
          className="w-4 h-4 text-gray-400 animate-pulse"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
          />
        </svg>
      );
    case "sent":
      return (
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M5 13l4 4L19 7"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
          />
        </svg>
      );
    case "delivered":
      return (
        <div className="flex">
          <svg
            className="w-4 h-4 text-gray-400 -mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M5 13l4 4L19 7"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
          <svg
            className="w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M5 13l4 4L19 7"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        </div>
      );
    case "read":
      return (
        <div className="flex">
          <svg
            className="w-4 h-4 text-blue-500 -mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M5 13l4 4L19 7"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
          <svg
            className="w-4 h-4 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M5 13l4 4L19 7"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        </div>
      );
    case "failed":
      return (
        <svg
          className="w-4 h-4 text-red-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
          />
        </svg>
      );
    default:
      return null;
  }
};

// Message type renderers
const TextMessage = ({ content }: { content: string }) => (
  <div className="break-words whitespace-pre-wrap">{content}</div>
);

const MediaMessage = ({ message }: { message: NormalizedMessage }) => {
  const getMediaIcon = () => {
    switch (message.type) {
      case "image":
        return "🖼️";
      case "audio":
        return "🎵";
      case "video":
        return "🎥";
      case "document":
        return "📄";
      default:
        return "📎";
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <span className="text-lg">{getMediaIcon()}</span>
      <div>
        <p className="font-medium">
          {message.type.charAt(0).toUpperCase() + message.type.slice(1)}
        </p>
        {message.content && (
          <p className="text-sm opacity-75">{message.content}</p>
        )}
      </div>
    </div>
  );
};

const LocationMessage = ({ content }: { content: string }) => (
  <div className="flex items-center space-x-2">
    <span className="text-lg">📍</span>
    <div>
      <p className="font-medium">Location</p>
      <p className="text-sm opacity-75">{content}</p>
    </div>
  </div>
);

const ContactMessage = ({ content }: { content: string }) => (
  <div className="flex items-center space-x-2">
    <span className="text-lg">👤</span>
    <div>
      <p className="font-medium">Contact</p>
      <p className="text-sm opacity-75">{content}</p>
    </div>
  </div>
);

const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

const MessageBubble: React.FC<MessageBubbleProps> = memo(
  ({ message, isFirstInGroup, isLastInGroup }) => {
    const isOwnMessage = message.sender.type === "user";

    const renderMessageContent = () => {
      switch (message.type) {
        case "text":
          return <TextMessage content={message.content} />;
        case "image":
        case "audio":
        case "video":
        case "document":
          return <MediaMessage message={message} />;
        case "location":
          return <LocationMessage content={message.content} />;
        case "contact":
          return <ContactMessage content={message.content} />;
        default:
          return <TextMessage content={message.content} />;
      }
    };

    return (
      <div
        className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} ${isLastInGroup ? "mb-4" : "mb-1"}`}
      >
        <div
          className={`flex items-end space-x-2 max-w-xs lg:max-w-md xl:max-w-lg ${isOwnMessage ? "flex-row-reverse space-x-reverse" : ""}`}
        >
          {/* Avatar (only for contact messages and first in group) */}
          {!isOwnMessage && isLastInGroup && (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center flex-shrink-0">
              <span className="text-white text-xs font-medium">
                {message.sender.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}

          {!isOwnMessage && !isLastInGroup && (
            <div className="w-8 h-8" /> // Spacer for alignment
          )}

          {/* Message Bubble */}
          <div
            className={`
          px-4 py-2 shadow-sm max-w-full
          ${
            isOwnMessage
              ? "bg-blue-500 text-white"
              : "bg-white dark:bg-gray-800 dark:text-white border border-gray-200 dark:border-gray-700"
          }
          ${
            isFirstInGroup && isLastInGroup
              ? "rounded-2xl"
              : isFirstInGroup
                ? isOwnMessage
                  ? "rounded-2xl rounded-br-md"
                  : "rounded-2xl rounded-bl-md"
                : isLastInGroup
                  ? isOwnMessage
                    ? "rounded-2xl rounded-tr-md"
                    : "rounded-2xl rounded-tl-md"
                  : isOwnMessage
                    ? "rounded-r-2xl rounded-l-md"
                    : "rounded-l-2xl rounded-r-md"
          }
        `}
          >
            {/* Reply indicator */}
            {message.replyTo && (
              <div
                className={`
              text-xs mb-2 pb-2 border-l-2 pl-2 
              ${
                isOwnMessage
                  ? "border-blue-300 text-blue-100"
                  : "border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-300"
              }
            `}
              >
                <p className="font-medium">{message.replyTo.sender}</p>
                <p className="truncate">{message.replyTo.content}</p>
              </div>
            )}

            {/* Message content */}
            <div className="text-sm">{renderMessageContent()}</div>

            {/* Message metadata */}
            <div
              className={`
            flex items-center justify-end mt-1 space-x-1
            ${isOwnMessage ? "text-blue-100" : "text-gray-500 dark:text-gray-300"}
          `}
            >
              {/* Forwarded indicator */}
              {message.metadata?.forwarded && (
                <span className="text-xs italic">Forwarded</span>
              )}

              {/* Timestamp */}
              <span className="text-xs">{formatTime(message.timestamp)}</span>

              {/* Status (only for own messages) */}
              {isOwnMessage && <MessageStatusIcon status={message.status} />}
            </div>

            {/* Reactions */}
            {message.reactions && message.reactions.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {message.reactions.map((reaction, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700"
                  >
                    {reaction.emoji}
                    <span className="ml-1 text-gray-600 dark:text-gray-300">
                      1
                    </span>
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
);

MessageBubble.displayName = "MessageBubble";

export default MessageBubble;
