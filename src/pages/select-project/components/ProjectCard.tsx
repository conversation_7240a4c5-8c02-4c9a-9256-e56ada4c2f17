import React, { useMemo } from "react";
import { Card, CardBody } from "@heroui/react";
import { Calendar, Phone, ArrowRight } from "lucide-react";

import ProjectLogo from "./ProjectLogo";

import { ProjectCardProps } from "@/types/project";

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onProjectSelect,
}) => {
  const { whatsappCredentials } = project;

  const formattedDate = useMemo(() => {
    const dateFormatter = new Intl.DateTimeFormat(undefined, {
      dateStyle: "medium",
    });

    return dateFormatter.format(new Date(project.createdAt));
  }, [project.createdAt]);

  const handleProjectSelect = () => {
    onProjectSelect(project);
  };

  const handleCtaPress = (event?: React.MouseEvent | React.KeyboardEvent) => {
    // Prevent the card's onPress from firing when clicking the CTA
    if (event) {
      event.stopPropagation();
    }
    handleProjectSelect();
  };

  return (
    <Card
      isPressable
      className="group h-full border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:-translate-y-1 hover:border-primary/50 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800"
      onPress={handleProjectSelect}
    >
      <CardBody className="flex h-full flex-col gap-4 p-5">
        <div className="flex items-start gap-3">
          <ProjectLogo name={project.projectName} />
          <div className="min-w-0 flex-1">
            <h3 className="truncate text-lg font-semibold text-gray-900 dark:text-gray-100">
              {project.projectName}
            </h3>
            <p className="mt-1 line-clamp-2 text-sm text-gray-600 dark:text-gray-300">
              {project.description}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-2 text-sm text-gray-500 dark:text-gray-400 sm:grid-cols-2">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400 dark:text-gray-500" />
            <span className="truncate">Created {formattedDate}</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-gray-400 dark:text-gray-500" />
            <span className="truncate">
              {whatsappCredentials.phoneNumber || "Phone number pending"}
            </span>
          </div>
        </div>

        <div className="mt-auto flex items-center justify-between pt-2">
          <span className="text-xs font-medium uppercase tracking-wide text-gray-400 transition-colors duration-300 group-hover:text-primary dark:text-gray-500">
            Tap to manage project
          </span>
          <div
            className="inline-flex cursor-pointer items-center justify-center gap-2 rounded-small bg-primary px-4 py-2 text-sm font-normal text-primary-foreground shadow-sm transition-all duration-300 hover:opacity-90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-focus dark:bg-primary dark:text-primary-foreground"
            role="button"
            tabIndex={0}
            onClick={handleCtaPress}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                handleCtaPress(e);
              }
            }}
          >
            <span>Open project</span>
            <ArrowRight className="h-4 w-4" />
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ProjectCard;
