import { useEffect } from "react";

import ChatLayout from "../../components/chat/ChatLayout";

import { useAppDispatch } from "@/store/hooks.ts";
import { setChats, setMessages } from "@/store/chatSlice.ts";
import { setInitializing } from "@/store/uiSlice.ts";
import { initializeMockData } from "@/data/mockData.ts";
import DefaultLayout from "@/layouts/default.tsx";

export default function Chats() {
  const dispatch = useAppDispatch();

  // Initialize app with mock data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        dispatch(setInitializing(true));

        // Load mock data
        const mockData = initializeMockData();

        // Set chats and messages in store
        dispatch(setChats(mockData.chats));

        // Initialize messages in store
        dispatch(setMessages(mockData.messages));
      } catch (error) {
        console.error("Failed to initialize app:", error);
      } finally {
        dispatch(setInitializing(false));
      }
    };

    initializeApp();
  }, [dispatch]);

  return (
    <DefaultLayout>
      <ChatLayout />
    </DefaultLayout>
  );
}
