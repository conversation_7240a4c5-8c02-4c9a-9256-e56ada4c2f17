// Application-level types for normalized chat data
export type MessageContentType =
  | "text"
  | "image"
  | "audio"
  | "video"
  | "document"
  | "sticker"
  | "location"
  | "contact"
  | "button_reply"
  | "list_reply"
  | "reaction"
  | "system";

export type MessageStatus =
  | "sending"
  | "sent"
  | "delivered"
  | "read"
  | "failed";

// Chat List Item (derived from WhatsApp contacts and conversations)
export interface ChatListItem {
  id: string; // Chat ID (usually phone number)
  name: string; // Contact name or phone number
  phoneNumber: string; // WhatsApp phone number
  whatsappId: string; // WhatsApp ID (wa_id)
  avatar?: string;
  lastMessage: {
    content: string;
    timestamp: string; // ISO format string
    sender: "user" | "contact";
    type: MessageContentType;
    whatsappMessageId?: string;
  };
  unreadCount: number;
  isBlocked: boolean;
  isMuted: boolean;
  labels: string[];
  conversationData?: {
    id: string;
    expirationTimestamp?: string; // ISO format string
    origin: {
      type: "marketing" | "utility" | "authentication" | "service";
    };
  };
}

// Contact Profile (extended with WhatsApp data)
export interface ContactProfile {
  id: string;
  name: string;
  phoneNumber: string;
  whatsappId: string; // wa_id from WhatsApp
  email?: string;
  avatar?: string;
  lastSeen?: string; // ISO format string
  about?: string;
  isBlocked: boolean;
  isMuted: boolean;
  labels: string[];

  // WhatsApp specific data
  profile?: {
    name: string;
  };
  businessProfile?: {
    displayPhoneNumber: string;
    phoneNumberId: string;
  };

  // Extended contact information (from contact messages)
  contactInfo?: {
    addresses?: {
      city?: string;
      country?: string;
      state?: string;
      street?: string;
      type?: "HOME" | "WORK";
    }[];
    emails?: {
      email: string;
      type?: "HOME" | "WORK";
    }[];
    organization?: {
      company?: string;
      department?: string;
      title?: string;
    };
    urls?: {
      url: string;
      type?: "HOME" | "WORK";
    }[];
  };
}

// Normalized Message Structure for Application Use
export interface NormalizedMessage {
  id: string;
  chatId: string; // Derived from sender's phone number
  whatsappMessageId: string; // Original WhatsApp message ID
  content: string;
  type: MessageContentType;
  sender: {
    id: string;
    name: string;
    phoneNumber: string;
    whatsappId: string;
    type: "user" | "contact";
  };
  timestamp: string; // ISO format string
  status: MessageStatus;
  replyTo?: {
    messageId: string;
    content: string;
    sender: string;
  };
  reactions?: MessageReaction[];
  attachments?: MessageAttachment[];
  metadata?: {
    forwarded?: boolean;
    frequentlyForwarded?: boolean;
    isFromAd?: boolean;
    referralData?: any; // WhatsAppReferralMessage from whatsapp.ts
  };
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: string; // ISO format string
}

export interface MessageAttachment {
  id: string;
  type: "image" | "audio" | "video" | "document";
  filename?: string;
  mimeType: string;
  size?: number;
  url?: string; // Retrieved from WhatsApp Media API
  thumbnailUrl?: string;
  caption?: string;
}

// Filter and Search Types
export interface FilterCriteria {
  searchQuery: string;
  filterBy: "name" | "phone" | "label" | "all";
  sortBy: "recent" | "alphabetical" | "unread";
  showOnlyUnread: boolean;
  selectedLabels: string[];
}

// UI Component Props Types
export interface MenuItemConfig {
  id: string;
  label: string;
  icon: React.ComponentType;
  href?: string;
  onClick?: () => void;
  badge?: number;
  isActive?: boolean;
}

export interface MainMenuProps {
  isExpanded: boolean;
  onToggle: () => void;
  menuItems: MenuItemConfig[];
  user: UserProfile;
}

export interface UserProfile {
  id: string;
  name: string;
  avatar?: string;
  phoneNumber: string;
}

// Menu States and Behavior
export enum MenuState {
  COLLAPSED = "collapsed", // 64px width, icons only
  EXPANDED = "expanded", // 240px width, icons + labels
  MOBILE_HIDDEN = "hidden", // Hidden on mobile
  MOBILE_OVERLAY = "overlay", // Full overlay on mobile
}

export interface MenuTransitions {
  duration: string;
  easing: string;
  properties: string[];
}

export interface MenuResponsiveBehavior {
  mobile: {
    state: MenuState;
    toggleTrigger: string;
    overlayOnExpand: boolean;
    backdrop: boolean;
  };
  tablet: {
    state: MenuState;
    expandTrigger: string;
    overlayOnExpand: boolean;
    backdrop: boolean;
  };
  desktop: {
    state: MenuState;
    expandTrigger: string;
    overlayOnExpand: boolean;
    backdrop: boolean;
  };
}

// Screen Size Types
export type ScreenSize = "mobile" | "tablet" | "desktop";

// Component Layout Props
export interface ChatLayoutProps {
  children?: React.ReactNode;
}

export interface LeftPanelProps {
  className?: string;
}

export interface CenterPanelProps {
  className?: string;
}

export interface RightPanelProps {
  className?: string;
}
