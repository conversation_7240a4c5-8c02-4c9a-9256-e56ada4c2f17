import { useEffect, useRef } from "react";

import { useAppSelector } from "../../../store/hooks";

import MessageBubble from "./MessageBubble";

interface MessageAreaProps {
  chatId: string;
}

const MessageArea: React.FC<MessageAreaProps> = ({ chatId }) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const { messages } = useAppSelector((state) => state.chat);

  const chatMessages = messages[chatId] || [];

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Group messages by date
  const groupedMessages = chatMessages.reduce(
    (groups, message) => {
      const date = new Date(message.timestamp).toDateString();

      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);

      return groups;
    },
    {} as Record<string, typeof chatMessages>,
  );

  const formatDateHeader = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);

    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString([], {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  };

  if (chatMessages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-gray-400 dark:text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </div>
          <p className="text-gray-500 dark:text-gray-400">
            No messages yet. Start the conversation!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={scrollRef}
      className="flex-1 overflow-y-auto p-4 space-y-4 bg-white dark:bg-gray-800"
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      {Object.entries(groupedMessages).map(([date, messagesForDate]) => (
        <div key={date}>
          {/* Date Separator */}
          <div className="flex items-center justify-center mb-4">
            <div className="bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-sm border border-gray-200 dark:border-gray-600">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-300">
                {formatDateHeader(date)}
              </span>
            </div>
          </div>

          {/* Messages for this date */}
          <div className="space-y-2">
            {messagesForDate.map((message, index) => {
              const prevMessage = index > 0 ? messagesForDate[index - 1] : null;
              const nextMessage =
                index < messagesForDate.length - 1
                  ? messagesForDate[index + 1]
                  : null;

              // Group consecutive messages from the same sender
              const isFirstInGroup =
                !prevMessage ||
                prevMessage.sender.id !== message.sender.id ||
                new Date(message.timestamp).getTime() -
                  new Date(prevMessage.timestamp).getTime() >
                  300000; // 5 minutes

              const isLastInGroup =
                !nextMessage ||
                nextMessage.sender.id !== message.sender.id ||
                new Date(nextMessage.timestamp).getTime() -
                  new Date(message.timestamp).getTime() >
                  300000; // 5 minutes

              return (
                <MessageBubble
                  key={message.id}
                  isFirstInGroup={isFirstInGroup}
                  isLastInGroup={isLastInGroup}
                  message={message}
                />
              );
            })}
          </div>
        </div>
      ))}

      {/* Typing indicator placeholder */}
      {/* You can add a typing indicator component here */}
    </div>
  );
};

export default MessageArea;
