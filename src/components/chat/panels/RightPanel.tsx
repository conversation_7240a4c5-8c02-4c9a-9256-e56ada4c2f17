import type { RightPanelProps, ChatListItem } from "../../../types";

import { But<PERSON> } from "@heroui/button";

import { useAppSelector, useAppDispatch } from "../../../store/hooks";
import { closeRightPanel } from "../../../store/uiSlice";

// Icons
const CloseIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M6 18L18 6M6 6l12 12"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const MuteIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
    <path
      d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const BlockIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const DeleteIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const MediaIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const DocumentIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const LinkIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const RightPanel: React.FC<RightPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { activeChatId, chats } = useAppSelector((state) => state.chat);

  const activeChat: ChatListItem | null = activeChatId
    ? chats.find((chat) => chat.id === activeChatId) || null
    : null;

  const handleClose = () => {
    dispatch(closeRightPanel());
  };

  if (!activeChat) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">No chat selected</p>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col h-full bg-white dark:bg-gray-800 ${className}`}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-gray-900 dark:text-white">
            Contact Info
          </h2>
          <Button
            isIconOnly
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            variant="light"
            onPress={handleClose}
          >
            <CloseIcon />
          </Button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Profile Section */}
        <div className="p-6 text-center border-b border-gray-200 dark:border-gray-700">
          <div className="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600">
            {activeChat.avatar ? (
              <img
                alt={activeChat.name}
                className="w-full h-full object-cover"
                src={activeChat.avatar}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500 text-white font-bold text-2xl">
                {activeChat.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>

          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {activeChat.name}
          </h3>

          <p className="text-gray-600 dark:text-gray-400 mb-3">
            {activeChat.phoneNumber}
          </p>

          {/* Labels */}
          {activeChat.labels.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mt-3">
              {activeChat.labels.map((label) => (
                <span
                  key={label}
                  className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-1 rounded-full"
                >
                  {label}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Media, Links & Docs */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3">
            Media, Links and Docs
          </h4>

          <div className="space-y-3">
            <Button
              className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              variant="light"
            >
              <div className="flex items-center space-x-3">
                <MediaIcon />
                <span className="text-gray-700 dark:text-gray-300">Media</span>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                23
              </span>
            </Button>

            <Button
              className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              variant="light"
            >
              <div className="flex items-center space-x-3">
                <DocumentIcon />
                <span className="text-gray-700 dark:text-gray-300">
                  Documents
                </span>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                5
              </span>
            </Button>

            <Button
              className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              variant="light"
            >
              <div className="flex items-center space-x-3">
                <LinkIcon />
                <span className="text-gray-700 dark:text-gray-300">Links</span>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                12
              </span>
            </Button>
          </div>
        </div>

        {/* Chat Settings */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3">
            Chat Settings
          </h4>

          <div className="space-y-2">
            <Button
              className="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              variant="light"
            >
              <MuteIcon />
              <span className="text-gray-700 dark:text-gray-300">
                {activeChat.isMuted
                  ? "Unmute notifications"
                  : "Mute notifications"}
              </span>
            </Button>

            <Button
              className="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              variant="light"
            >
              <span className="text-gray-700 dark:text-gray-300">
                Search messages
              </span>
            </Button>
          </div>
        </div>

        {/* Danger Zone */}
        <div className="p-4">
          <div className="space-y-2">
            <Button
              className="w-full flex items-center space-x-3 p-3 text-left text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
              variant="light"
            >
              <BlockIcon />
              <span>
                {activeChat.isBlocked ? "Unblock contact" : "Block contact"}
              </span>
            </Button>

            <Button
              className="w-full flex items-center space-x-3 p-3 text-left text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
              variant="light"
            >
              <DeleteIcon />
              <span>Delete chat</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RightPanel;
