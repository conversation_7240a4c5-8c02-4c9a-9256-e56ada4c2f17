import { useState, useEffect } from "react";
import { Button, Input, Link } from "@heroui/react";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import { signUp } from "@/services/main/registerMainService.ts";

interface FormErrors {
  fullName?: string;
  email?: string;
  password?: string;
}

export default function SignUp() {
  const auth = useAuth();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [generalError, setGeneralError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setFormErrors({});
    setGeneralError(null);

    try {
      // Register the user
      await signUp(email, password, name);

      // Auto-login after successful registration
      await auth.login(email, password);
    } catch (err: any) {
      setLoading(false);

      // Parse error response
      if (
        err.response?.data?.errors &&
        Array.isArray(err.response.data.errors)
      ) {
        // Handle validation errors
        const errors: FormErrors = {};

        err.response.data.errors.forEach((error: any) => {
          const field = error.path?.[0];

          if (field && typeof field === "string") {
            if (errors[field as keyof FormErrors]) {
              errors[field as keyof FormErrors] += "; " + error.message;
            } else {
              errors[field as keyof FormErrors] = error.message;
            }
          }
        });
        setFormErrors(errors);
      }

      if (err.response?.data?.message) {
        setGeneralError(err.response.data.message);
        // Special handling for duplicate email
        if (err.response.data.message.includes("already in use")) {
          setFormErrors((prev) => ({
            ...prev,
            email: err.response.data.message,
          }));
        }
      } else if (!err.response?.data?.errors) {
        // Fallback error message
        setGeneralError("Failed to create account. Please try again.");
      }
      console.error("Signup error:", err);

      return;
    }
  };

  useEffect(() => {
    if (auth.state === "loggedIn") {
      setTimeout(() => {
        window.location.href = "/";
      }, 1000);
    }
  }, [auth.state]);

  const inputClassNames = {
    input:
      "text-gray-700 placeholder:text-gray-400 dark:text-gray-100 dark:placeholder:text-gray-500",
    inputWrapper:
      "bg-gray-50 border border-gray-200 hover:border-gray-300 transition-colors dark:bg-gray-800 dark:border-gray-700 dark:hover:border-gray-600",
    label: "text-gray-600 dark:text-gray-300",
  };

  return (
    <div className="min-h-screen flex bg-gray-50 dark:bg-gray-950">
      {/* Left side - Background Image */}
      <div
        className="hidden lg:flex lg:w-2/3 relative bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=people%20working%20together%20in%20a%20modern%20office%20with%20large%20windows%20and%20natural%20light%2C%20focusing%20on%20a%20project%20&image_size=landscape_16_9')`,
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20 dark:bg-black/40" />

      </div>

      {/* Right side - SignUp Form */}
      <div className="w-full lg:w-1/3 flex flex-col justify-center p-12 bg-white dark:bg-gray-900">
        {/* Main Content Container */}
        <div className="flex flex-col space-y-8 max-w-sm mx-auto w-full">
          {/* Header with Logo */}
          <div className="flex items-center justify-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">TI</span>
            </div>
            <span className="ml-3 text-xl font-semibold text-gray-900 dark:text-gray-100">
              Trimitra Ideal
            </span>
          </div>

          {/* Welcome Text */}
          <div className="text-center space-y-2">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
              Create your account
            </h1>
          </div>

          {/* SignUp Form */}
          <div className="space-y-6">
            {generalError && (
              <div
                className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-600 dark:border-red-500/40 dark:bg-red-950 dark:text-red-200"
                role="alert"
              >
                {generalError}
              </div>
            )}
            {auth.errorMessages && (
              <div
                className="rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-600 dark:border-red-500/40 dark:bg-red-950 dark:text-red-200"
                role="alert"
              >
                {auth.errorMessages}
              </div>
            )}
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <Input
                    required
                    aria-describedby={
                      formErrors.fullName ? "fullName-error" : undefined
                    }
                    classNames={inputClassNames}
                    disabled={loading}
                    isInvalid={!!formErrors.fullName}
                    label="Name"
                    labelPlacement="inside"
                    placeholder="Enter your name"
                    size="lg"
                    type="text"
                    value={name}
                    variant="flat"
                    onChange={(e) => {
                      setName(e.target.value);
                      // Clear field-level error when user starts typing
                      if (formErrors.fullName) {
                        setFormErrors(prev => {
                          const newErrors = { ...prev };
                          delete newErrors.fullName;
                          return newErrors;
                        });
                      }
                      // Clear general error when user starts typing
                      if (generalError) {
                        setGeneralError(null);
                      }
                    }}
                  />
                  {formErrors.fullName && (
                    <div
                      className="text-red-500 text-sm mt-1"
                      id="fullName-error"
                      role="alert"
                    >
                      {formErrors.fullName}
                    </div>
                  )}
                </div>

                <div>
                  <Input
                    required
                    aria-describedby={
                      formErrors.email ? "email-error" : undefined
                    }
                    classNames={inputClassNames}
                    disabled={loading}
                    isInvalid={!!formErrors.email}
                    label="Email"
                    labelPlacement="inside"
                    placeholder="Enter your email"
                    size="lg"
                    type="email"
                    value={email}
                    variant="flat"
                    onChange={(e) => {
                      setEmail(e.target.value);
                      // Clear field-level error when user starts typing
                      if (formErrors.email) {
                        setFormErrors(prev => {
                          const newErrors = { ...prev };
                          delete newErrors.email;
                          return newErrors;
                        });
                      }
                      // Clear general error when user starts typing, especially for email errors
                      if (generalError) {
                        setGeneralError(null);
                      }
                    }}
                  />
                  {formErrors.email && (
                    <div
                      className="text-red-500 text-sm mt-1"
                      id="email-error"
                      role="alert"
                    >
                      {formErrors.email}
                    </div>
                  )}
                </div>

                <div>
                  <Input
                    required
                    aria-describedby={
                      formErrors.password ? "password-error" : undefined
                    }
                    classNames={inputClassNames}
                    disabled={loading}
                    isInvalid={!!formErrors.password}
                    label="Password"
                    labelPlacement="inside"
                    placeholder="Enter password"
                    size="lg"
                    type="password"
                    value={password}
                    variant="flat"
                    onChange={(e) => {
                      setPassword(e.target.value);
                      // Clear field-level error when user starts typing
                      if (formErrors.password) {
                        setFormErrors(prev => {
                          const newErrors = { ...prev };
                          delete newErrors.password;
                          return newErrors;
                        });
                      }
                      // Clear general error when user starts typing
                      if (generalError) {
                        setGeneralError(null);
                      }
                    }}
                  />
                  {formErrors.password && (
                    <div
                      className="text-red-500 text-sm mt-1"
                      id="password-error"
                      role="alert"
                    >
                      {formErrors.password}
                    </div>
                  )}
                </div>
              </div>

              <Button
                className="w-full bg-blue-600 hover:bg-blue-700 font-semibold dark:bg-blue-500 dark:hover:bg-blue-400"
                color="primary"
                disabled={loading}
                isLoading={loading}
                size="lg"
                type="submit"
              >
                {loading ? "Creating account..." : "Sign up"}
              </Button>
            </form>

            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Already have an account?{" "}
              </span>
              <Link
                className="text-blue-600 hover:text-blue-700 font-medium dark:text-blue-400 dark:hover:text-blue-300"
                href="#"
                size="sm"
              >
                Sign in
              </Link>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between text-sm text-gray-500 mt-8 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">F</span>
              </div>
              <span className="text-blue-600 dark:text-blue-400">
                @trimitra
              </span>
            </div>
            <span>© Trimitra Ideal 2024</span>
          </div>
        </div>
      </div>
    </div>
  );
}
