# Dark Mode Implementation Guide

This document explains how dark mode works in the Ideal chat experience and documents the styling primitives that keep the UI consistent. Use it whenever you touch theming, add pages, or debug user reports about color scheme issues.

## Architecture Overview

- Tailwind is configured with `darkMode: "class"` (`tailwind.config.js`), so the presence of the `dark` class on `<html>` drives variant styles.
- The Redux `ui` slice owns the `theme` state (`light | dark`), with `toggleTheme` and `setTheme` reducers (`src/store/uiSlice.ts`).
- `ThemeObserver` (`src/components/theme/ThemeObserver.tsx`) mounts once inside `Provider` and keeps the DOM, localStorage, and store in sync.
- HeroUI components consume Tailwind classes through `className`/`classNames` props; combine Tailwind utilities with HeroUI color props instead of hard-coding new tokens.

## Theme Initialization Flow

1. On boot, `ThemeObserver` runs and chooses the effective theme with this priority order:
   - Saved user preference in `localStorage.theme`.
   - System preference from `window.matchMedia('(prefers-color-scheme: dark)')`.
   - Default fallback (`light`).
2. The chosen value is dispatched to the Redux store if it differs from the current state.
3. The `<html>` element receives or loses the `dark` class, and `html.dark { color-scheme: dark; }` in `src/styles/globals.css` updates native form controls and scrollbars.
4. Every subsequent theme change (manual toggle, settings update, etc.) re-applies the class and persists the value back to `localStorage`.

## Toggling the Theme

- `DarkModeToggle` (`src/components/theme/DarkModeToggle.tsx`) presents a compact switch with Sun/Moon icons for quick access (e.g., top navigation).
- The Settings screen uses `AppearanceSection` (`src/pages/settings/components/AppearanceSection.tsx`) to render the same toggle alongside other display preferences.
- Both components dispatch `toggleTheme()`, so any new consumer should import the same action. Avoid duplicating local state to prevent desynchronization with `ThemeObserver`.
- When rendering switches, use the Redux `theme` selector for the `isSelected` state and rely on HeroUI’s `onChange`/`onValueChange` signatures.

## Styling Guidelines

- Surfaces:
  - App shell: `bg-gray-50 dark:bg-gray-900` (see `src/layouts/default.tsx`).
  - Cards/containers: `bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700`.
  - Subtle panels or overlays: `dark:bg-gray-700/30` keeps translucency without blowing contrast.
- Text:
  - Headings: `text-gray-900 dark:text-gray-100`.
  - Body copy: `text-gray-700 dark:text-gray-300`.
  - Muted and helper text: `text-gray-500 dark:text-gray-400`.
  - Placeholders: `placeholder:text-gray-400 dark:placeholder:text-gray-500`.
- Icons and interactive affordances:
  - Base: `text-gray-400` in dark mode.
  - Hover states: `dark:hover:text-gray-200` for neutral, `dark:hover:text-primary`/`dark:hover:text-danger` for semantic variants.
- Inputs & form controls:
  - Wrapper backgrounds: `bg-gray-50 dark:bg-gray-700`.
  - Borders: `border-gray-200 dark:border-gray-600`.
  - Labels: add `dark:text-gray-300` if the default utility does not cover it.
- When customizing HeroUI components, use the `classNames` API to target internal slots without breaking light theme defaults. Example:

  ```tsx
  <Input
    classNames={{
      inputWrapper:
        "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
      label: "text-gray-700 dark:text-gray-300",
    }}
  />
  ```

## Sample Implementations

```tsx
// Card container
<Card className="bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700" />

// Icon-only button
<Button
  isIconOnly
  variant="light"
  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
>
  <MoreHorizontal />
</Button>
```

## Accessibility Targets

- Body text on `gray-800` surfaces ≥ 4.5:1 contrast ratio.
- Secondary text on `gray-800` surfaces ≥ 3:1.
- Focus outlines must remain visible; prefer `focus:ring-primary` with `dark:focus:ring-primary/70` where needed.
- Verify custom gradients and overlays in dark mode to avoid lowering text contrast below AA.

## Testing Checklist

- **Local preference**: Toggle the switch, refresh, and confirm the previous choice persists (inspect `localStorage.theme`).
- **System preference**: Clear the key (`localStorage.removeItem('theme')`), reload, and ensure the UI follows your OS color scheme.
- **SSR safety**: New components should avoid reading `window` during render; use effects/hooks instead so dark mode loads without hydration mismatches.
- **Visual sweep**: In dark mode, review app shell, cards, inputs, and any new feature specific screens for missing `dark:` variants.

Update this guide whenever you introduce new primitives or adjust the palette so the rest of the team can follow the same conventions.
