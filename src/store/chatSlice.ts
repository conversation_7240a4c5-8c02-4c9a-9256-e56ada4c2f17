import type { ChatListItem, NormalizedMessage } from "@/types";

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ChatState {
  chats: ChatListItem[];
  messages: Record<string, NormalizedMessage[]>;
  activeChatId: string | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
}

const initialState: ChatState = {
  chats: [],
  messages: {},
  activeChatId: null,
  isLoading: false,
  error: null,
  searchQuery: "",
};

export const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setActiveChat: (state, action: PayloadAction<string>) => {
      state.activeChatId = action.payload;
      // Mark messages as read when chat is opened
      const messages = state.messages[action.payload];

      if (messages) {
        messages.forEach((message) => {
          if (message.sender.type === "contact" && message.status !== "read") {
            message.status = "read";
          }
        });
      }
      // Update unread count
      const chat = state.chats.find((c) => c.id === action.payload);

      if (chat) {
        chat.unreadCount = 0;
      }
    },

    addMessage: (state, action: PayloadAction<NormalizedMessage>) => {
      const message = action.payload;

      if (!state.messages[message.chatId]) {
        state.messages[message.chatId] = [];
      }
      state.messages[message.chatId].push(message);

      // Update chat's last message
      const chat = state.chats.find((c) => c.id === message.chatId);

      if (chat) {
        chat.lastMessage = {
          content: message.content,
          timestamp: message.timestamp,
          sender: message.sender.type,
          type: message.type,
          whatsappMessageId: message.whatsappMessageId,
        };

        // Increment unread if message is from contact and chat is not active
        if (
          message.sender.type === "contact" &&
          state.activeChatId !== message.chatId
        ) {
          chat.unreadCount += 1;
        }
      }
    },

    sendMessage: (
      state,
      action: PayloadAction<{
        chatId: string;
        content: string;
        type: "text" | "image" | "audio" | "video" | "document";
      }>,
    ) => {
      const { chatId, content, type } = action.payload;
      const message: NormalizedMessage = {
        id: crypto.randomUUID(),
        chatId,
        whatsappMessageId: `temp_${Date.now()}`,
        content,
        type,
        sender: {
          id: "current-user",
          name: "You",
          phoneNumber: "+1234567890",
          whatsappId: "user_wa_id",
          type: "user",
        },
        timestamp: new Date().toISOString(),
        status: "sending",
      };

      if (!state.messages[chatId]) {
        state.messages[chatId] = [];
      }
      state.messages[chatId].push(message);

      // Update chat's last message
      const chat = state.chats.find((c) => c.id === chatId);

      if (chat) {
        chat.lastMessage = {
          content,
          timestamp: message.timestamp,
          sender: "user",
          type,
          whatsappMessageId: message.whatsappMessageId,
        };
      }
    },

    updateMessageStatus: (
      state,
      action: PayloadAction<{
        messageId: string;
        status: "sent" | "delivered" | "read" | "failed";
      }>,
    ) => {
      const { messageId, status } = action.payload;

      Object.values(state.messages).forEach((chatMessages) => {
        const message = chatMessages.find((m) => m.id === messageId);

        if (message) {
          message.status = status;
        }
      });
    },

    setChats: (state, action: PayloadAction<ChatListItem[]>) => {
      state.chats = action.payload;
    },

    addChat: (state, action: PayloadAction<ChatListItem>) => {
      const existingIndex = state.chats.findIndex(
        (c) => c.id === action.payload.id,
      );

      if (existingIndex !== -1) {
        state.chats[existingIndex] = action.payload;
      } else {
        state.chats.unshift(action.payload);
      }
    },

    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    setMessages: (
      state,
      action: PayloadAction<Record<string, NormalizedMessage[]>>,
    ) => {
      state.messages = action.payload;
    },
  },
});

export const {
  setActiveChat,
  addMessage,
  sendMessage,
  updateMessageStatus,
  setChats,
  addChat,
  setSearchQuery,
  setLoading,
  setError,
  setMessages,
} = chatSlice.actions;

export default chatSlice.reducer;
