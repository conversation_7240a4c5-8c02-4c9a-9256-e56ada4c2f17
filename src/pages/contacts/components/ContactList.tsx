import React from "react";
import { useDispatch } from "react-redux";
import { Card, CardBody, Avatar, Button } from "@heroui/react";
import { Mail, Phone, MapPin, Edit, Trash2 } from "lucide-react";

import ContactCard from "./ContactCard";

import { ContactListProps } from "@/types/contact";
import { openModal, deleteContact } from "@/store/contactsSlice";

const ContactList: React.FC<ContactListProps> = ({
  contacts,
  viewMode,
  onContactSelect,
}) => {
  const dispatch = useDispatch();

  const handleEdit = (contact: any) => {
    dispatch(openModal({ mode: "edit", contact }));
  };

  const handleDelete = (contactId: string) => {
    if (window.confirm("Are you sure you want to delete this contact?")) {
      dispatch(deleteContact(contactId));
    }
  };

  if (viewMode === "grid") {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {contacts.map((contact) => (
          <div
            key={contact.id}
            className="cursor-pointer"
            onClick={() => onContactSelect(contact)}
          >
            <ContactCard
              contact={contact}
              onDelete={handleDelete}
              onEdit={handleEdit}
            />
          </div>
        ))}
      </div>
    );
  }

  // List view
  return (
    <div className="space-y-3">
      {contacts.map((contact) => (
        <Card
          key={contact.id}
          className="bg-white border border-gray-200 hover:shadow-md transition-shadow duration-200 dark:bg-gray-800 dark:border-gray-700"
        >
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div
                className="flex items-center gap-4 flex-1 cursor-pointer"
                onClick={() => onContactSelect(contact)}
              >
                <Avatar
                  className="flex-shrink-0"
                  name={contact.name}
                  size="md"
                  src={contact.avatar}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <div className="min-w-0">
                      <h3 className="font-semibold text-foreground truncate">
                        {contact.name}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Mail className="w-3 h-3" />
                          <span className="truncate">{contact.email}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Phone className="w-3 h-3" />
                          <span className="truncate">{contact.phone}</span>
                        </div>
                      </div>
                    </div>
                    {contact.address && (
                      <div className="flex items-center gap-1 text-sm text-gray-400 dark:text-gray-400">
                        <MapPin className="w-3 h-3 flex-shrink-0" />
                        <span className="truncate">{contact.address}</span>
                      </div>
                    )}
                  </div>
                  {contact.notes && (
                    <p className="text-sm text-gray-500 mt-2 line-clamp-1 dark:text-gray-400">
                      {contact.notes}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2 ml-4">
                <Button
                  isIconOnly
                  className="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
                  size="sm"
                  variant="light"
                  onPress={() => handleEdit(contact)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  isIconOnly
                  className="text-gray-500 hover:text-danger dark:text-gray-400 dark:hover:text-danger"
                  size="sm"
                  variant="light"
                  onPress={() => handleDelete(contact.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
};

export default ContactList;
