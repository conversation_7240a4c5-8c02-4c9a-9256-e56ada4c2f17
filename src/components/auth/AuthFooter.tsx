import React from "react";

interface AuthFooterProps {
  className?: string;
}

const AuthFooter: React.FC<AuthFooterProps> = ({ className = "" }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      className={`text-center text-sm text-gray-500 dark:text-gray-400 ${className}`}
    >
      <span>© {currentYear} Ideal Lumatera. All rights reserved.</span>
    </footer>
  );
};

export default AuthFooter;
