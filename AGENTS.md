# Repository Guidelines

## Project Structure & Module Organization

Source lives under `src/`, organized by feature slices: reusable UI in `components/` (chat panels under `components/chat/`), routed views in `pages/`, shared shells in `layouts/`, and Redux Toolkit state in `store/` (`chatSlice.ts`, `uiSlice.ts`, hooks). Shared contracts are in `types/`, while mock fixtures sit in `data/`. Styling utilities are centralized in `styles/`, and app bootstrap flows through `main.tsx`, `provider.tsx`, and `App.tsx`. Public assets belong in `public/`. Prefer the `@/` alias (`import { LeftPanel } from "@/components/chat/panels";`) instead of long relative paths.

## Build, Test, and Development Commands

`npm run dev` launches the Vite dev server with HMR. `npm run build` type-checks and emits production assets to `dist/`. `npm run preview` serves those built assets for sanity checks. `npm run lint` runs ESLint with Prettier integration; pair it with `npm run format` for bulk formatting. Vitest commands: `npm run test` (watch mode), `npm run test:run` (single pass), and `npm run test:coverage` when coverage thresholds matter.

## Coding Style & Naming Conventions

Write in strict TypeScript with React function components and hooks. Tailwind utilities and HeroUI widgets drive styling; keep dark-mode selectors via root `class`. Imports flow React → third-party → local, separated by blank lines. Components use PascalCase filenames, hooks/utilities camelCase, constants UPPER_SNAKE_CASE. Default formatting follows Prettier; never hand-edit generated Tailwind classes.

## Testing Guidelines

Use Vitest with Testing Library (`@testing-library/react`, `@testing-library/jest-dom`). Co-locate tests (`Component.test.tsx`) beside components or slices (`chatSlice.test.ts`). Assert render output, interactions, and reducer transitions; avoid snapshot-only suites. Run `npm run test:coverage` before major refactors to confirm behavior parity.

## Commit & Pull Request Guidelines

Commits follow Conventional Commits (`feat(chat): add message search`). Bundle minimal logical changes and ensure lint/tests pass. PRs should include a purpose-driven summary, screenshots or GIFs for UI patches, verification steps, and linked issues. Confirm console is clean, documentation is updated, and build artifacts are reproducible.

## Security & Configuration Tips

Keep secrets in `.env.local` using the `VITE_` prefix and load them via `import.meta.env`. Vercel deployments rely on SPA rewrites in `vercel.json`; always run `npm run build` to validate before pushing.
