import { ArrowLeft } from "lucide-react";
import { useNavigate, useOutletContext } from "react-router-dom";

import ProjectOverviewSection from "./components/ProjectOverviewSection";

import DefaultLayout from "@/layouts/default";
import { setMainMenuExpanded } from "@/store/uiSlice";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { Project } from "@/services/main/projectMainService";

const ProjectSettings = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);
  const { project } = useOutletContext<{
    project: Project | null;
    projectLoadStatus: string;
  }>();

  const leftMargin =
    visiblePanels.mainMenu && screenSize !== "mobile" ? "ml-14" : "ml-0";

  const handleBack = () => {
    navigate(-1);
  };

  const handleToggleMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };

  return (
    <DefaultLayout>
      <div
        className={`flex h-full ${leftMargin} transition-all duration-200 ease-out`}
      >
        {screenSize === "mobile" && !visiblePanels.mainMenu && (
          <button
            aria-label="Open menu"
            className="fixed top-4 left-4 z-30 p-2 rounded-md bg-white dark:bg-gray-800 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
            onClick={handleToggleMainMenu}
          >
            <svg
              className="w-6 h-6 text-gray-800 dark:text-gray-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M4 6h16M4 12h16M4 18h16"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </button>
        )}

        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-6 space-y-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <button
                  aria-label="Go back"
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  onClick={handleBack}
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Project Settings
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Configure project-specific workflows, access, and automation
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <ProjectOverviewSection project={project} />
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
};

export default ProjectSettings;
